export const navItems = [
  { name: "About", link: "#about" },
  { name: "Projects", link: "#projects" },
  { name: "Skills", link: "#skills" },
  { name: "Contact", link: "#contact" },
];

export const gridItems = [
  {
    id: 1,
    title:
      "I prioritize clean code and user-centric design, fostering collaborative development",
    description: "",
    className: "lg:col-span-3 md:col-span-6 md:row-span-4 lg:min-h-[60vh]",
    imgClassName: "w-full h-full",
    titleClassName: "justify-end",
    img: "/b1.svg",
    spareImg: "",
  },
  {
    id: 2,
    title: "Based in Bengaluru, available for remote collaboration",
    description: "",
    className: "lg:col-span-2 md:col-span-3 md:row-span-2",
    imgClassName: "",
    titleClassName: "justify-start",
    img: "",
    spareImg: "",
  },
  {
    id: 3,
    title: "My tech stack",
    description: "MERN Stack & Modern Web Technologies",
    className: "lg:col-span-2 md:col-span-3 md:row-span-2",
    imgClassName: "",
    titleClassName: "justify-center",
    img: "",
    spareImg: "",
  },
  {
    id: 4,
    title: "Full Stack Developer with passion for AI integration.",
    description: "",
    className: "lg:col-span-2 md:col-span-3 md:row-span-1",
    imgClassName: "",
    titleClassName: "justify-start",
    img: "/grid.svg",
    spareImg: "/b4.svg",
  },

  {
    id: 5,
    title: "Currently building AI-powered web applications",
    description: "The Inside Scoop",
    className: "md:col-span-3 md:row-span-2",
    imgClassName: "absolute right-0 bottom-0 md:w-96 w-60",
    titleClassName: "justify-center md:justify-start lg:justify-center",
    img: "/b5.svg",
    spareImg: "/grid.svg",
  },
  {
    id: 6,
    title: "Do you want to start a project together?",
    description: "",
    className: "lg:col-span-2 md:col-span-3 md:row-span-1",
    imgClassName: "",
    titleClassName: "justify-center md:max-w-full max-w-60 text-center",
    img: "",
    spareImg: "",
  },
];

export const projects = [
  {
    id: 1,
    title: "NaturalSQL",
    des: "AI-powered web app to convert natural language queries into SQL with intuitive interface and real-time processing.",
    img: "/p1.svg",
    iconLists: ["/re.svg", "/tail.svg", "/ts.svg", "/next.svg", "/fm.svg"],
    link: "https://saurabhdahariya.vercel.app/",
  },
  {
    id: 2,
    title: "Smart Portfolio",
    des: "AI-integrated portfolio with voice and text interaction capabilities, showcasing modern web development skills.",
    img: "/p2.svg",
    iconLists: ["/next.svg", "/tail.svg", "/ts.svg", "/re.svg", "/c.svg"],
    link: "https://saurabhdahariya.vercel.app/",
  },
  {
    id: 3,
    title: "QuizByAI",
    des: "Full-stack AI-driven quiz platform with Firebase authentication and comprehensive analytics dashboard.",
    img: "/p3.svg",
    iconLists: ["/re.svg", "/tail.svg", "/ts.svg", "/next.svg", "/c.svg"],
    link: "https://saurabhdahariya.vercel.app/",
  },
  {
    id: 4,
    title: "Portfolio Website",
    des: "Modern, responsive portfolio website built with Next.js, featuring smooth animations and interactive elements.",
    img: "/p4.svg",
    iconLists: ["/next.svg", "/tail.svg", "/ts.svg", "/re.svg", "/gsap.svg"],
    link: "https://saurabhdahariya.vercel.app/",
  },
];

export const testimonials = [
  // Testimonials section can be added later when available
];

export const companies = [
  {
    id: 1,
    name: "cloudinary",
    img: "/cloud.svg",
    nameImg: "/cloudName.svg",
  },
  {
    id: 2,
    name: "appwrite",
    img: "/app.svg",
    nameImg: "/appName.svg",
  },
  {
    id: 3,
    name: "HOSTINGER",
    img: "/host.svg",
    nameImg: "/hostName.svg",
  },
  {
    id: 4,
    name: "stream",
    img: "/s.svg",
    nameImg: "/streamName.svg",
  },
  {
    id: 5,
    name: "docker.",
    img: "/dock.svg",
    nameImg: "/dockerName.svg",
  },
];

export const workExperience = [
  {
    id: 1,
    title: "B.Tech Information Technology",
    desc: "Bhilai Institute of Technology, Durg (Sep 2019 - Jun 2023) - Strong foundation in programming and software development.",
    className: "md:col-span-2",
    thumbnail: "/exp1.svg",
  },
  {
    id: 2,
    title: "MERN Stack Development Training",
    desc: "JSpider BTM Layout, Bengaluru (Sep 2024 - Feb 2025) - Comprehensive training in full-stack web development.",
    className: "md:col-span-2",
    thumbnail: "/exp2.svg",
  },
  {
    id: 3,
    title: "Full Stack Developer",
    desc: "Specialized in React.js, Next.js, Node.js, and MongoDB with expertise in modern web technologies.",
    className: "md:col-span-2",
    thumbnail: "/exp3.svg",
  },
  {
    id: 4,
    title: "AI Integration Specialist",
    desc: "Experience with OpenAI API, AiMLAPI, and ElevenLabs API for building intelligent web applications.",
    className: "md:col-span-2",
    thumbnail: "/exp4.svg",
  },
];

export const socialMedia = [
  {
    id: 1,
    img: "/git.svg",
    link: "https://github.com/saurabhdahariya",
  },
  {
    id: 2,
    img: "/link.svg",
    link: "https://saurabhdahariya.vercel.app/",
  },
  {
    id: 3,
    img: "/wha.svg",
    link: "tel:+918319130513",
  },
];
